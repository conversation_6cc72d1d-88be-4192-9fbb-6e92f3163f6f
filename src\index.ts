import { Hono } from 'hono'
// This special import brings in your CSV file as plain text.
import csvData from '../vaktet.csv'

// Define a type for our prayer time entries for better code safety
type PrayerTime = {
  Date: string
  Imsaku: string
  Sabahu: string
  Lindja: string
  Dreka: string
  Ikindia: string
  Akshami: string
  Jacia: string
  Festat: string
  Shenime: string
}

// --- Data Processing ---
// We parse the CSV once when the worker starts and store it in a Map for fast lookups.
// A Map is like a dictionary, perfect for key-value access (e.g., "28-Sep" -> {data})
const prayerTimesMap = new Map<string, PrayerTime>()

const rows = csvData.trim().split('\n')
const headers = rows.shift()!.split(',') // Get and remove the header row

rows.forEach(row => {
  const values = row.split(',')
  const entry: any = {}
  headers.forEach((header, index) => {
    entry[header] = values[index] || '' // Ensure value is not undefined
  })
  prayerTimesMap.set(entry.Date, entry as PrayerTime)
})
// --- End Data Processing ---

const app = new Hono()

// API Endpoint: /api/vaktet?date=DD-Mon (e.g., /api/vaktet?date=28-Sep)
app.get('/api/vaktet', (c) => {
  let dateKey = c.req.query('date')

  // If no date is provided, use today's date
  if (!dateKey) {
    const today = new Date()
    const day = today.getDate()
    const month = today.toLocaleString('en-US', { month: 'short' }) // e.g., 'Jan', 'Sep'
    dateKey = `${day}-${month}`
  }

  const prayerData = prayerTimesMap.get(dateKey)

  // If no data is found for the given date, return a 404 error
  if (!prayerData) {
    return c.json({ error: `Nuk u gjetën të dhëna për datën: ${dateKey}` }, 404)
  }

  // --- Albanian Date Formatting ---
  // Create a full date object to be formatted
  const [day, monthStr] = prayerData.Date.split('-')
  const dateObj = new Date(`${monthStr} ${day}, ${new Date().getFullYear()}`)

  // Format the date into Albanian using the built-in Intl API
  const formattedDate = new Intl.DateTimeFormat('sq-AL', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  }).format(dateObj)
  // Result: "e shtunë, 28 shtator 2024"

  // Return the data in a clean JSON format
  return c.json({
    data: {
      ...prayerData,
      data_e_formatuar: formattedDate, // Add the formatted Albanian date
    }
  })
})

export default app